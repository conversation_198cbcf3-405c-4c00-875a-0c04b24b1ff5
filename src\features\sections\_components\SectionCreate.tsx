import { useForm } from '@tanstack/react-form'
import { useQuery, useMutation } from 'convex/react'
import { toast } from 'sonner'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Spinner } from '@/components/ui/Spinner'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog'
import { DiamondPlusIcon, CirclePlusIcon } from 'lucide-react'
import { api } from 'convex/_generated/api'
import { sectionSchema } from '../lib/schema'
import { formatErrorMessage } from '@/lib/errorUtils'
import type { AcademicYearId, TeacherId, TrackId, StrandId, MajorId } from '@/lib/types'
import { isSeniorHighSchool } from '../lib/utils'
import { Separator } from '@/components/ui/Separator'

export function SectionCreate({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const academicYears = useQuery(api.academicYears.getAll)
  const tracks = useQuery(api.tracks.getAll)
  const strands = useQuery(api.strands.getAll)
  const majors = useQuery(api.majors.getAll)
  const teachers = useQuery(api.teachers.getAvailableForAdvising, {})

  const createSection = useMutation(api.sections.create)

  const form = useForm({
    defaultValues: {
      name: '',
      gradeLevel: 7,
      academicYearId: academicYears?.[0]?._id ?? '',
      adviserId: teachers?.[0]?._id ?? '',
      trackId: undefined as TrackId | undefined,
      strandId: undefined as StrandId | undefined,
      majorId: undefined as MajorId | undefined,
      isActive: true,
    },
    onSubmit: async ({ value }) => {
      try {
        const sectionData = {
          name: value.name,
          gradeLevel: value.gradeLevel,
          academicYearId: value.academicYearId as AcademicYearId,
          adviserId: value.adviserId as TeacherId,
          trackId: isSeniorHighSchool(value.gradeLevel) ? value.trackId : undefined,
          strandId: isSeniorHighSchool(value.gradeLevel) ? value.strandId : undefined,
          majorId: isSeniorHighSchool(value.gradeLevel) ? value.majorId : undefined,
          isActive: value.isActive,
        }

        await createSection(sectionData)
        toast.success('Section created successfully')
        onOpenChange(false)
        form.reset()
      } catch (error) {
        toast.error(
          error instanceof Error ? formatErrorMessage(error) : 'An unexpected error occurred'
        )
      }
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DiamondPlusIcon className="text-primary" />
            Create New Section
          </DialogTitle>
          <DialogDescription>
            Fill in the details below to create a new section for students to enroll in.
          </DialogDescription>
        </DialogHeader>
        <Separator />
        <form
          onSubmit={(e) => {
            e.preventDefault()
            form.handleSubmit()
          }}
          className="space-y-4"
        >
          <div className="grid gap-6 md:grid-cols-2">
            {/* Section Name */}
            <form.Field
              name="name"
              validators={{
                onChange: sectionSchema.shape.name,
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>
                    Section Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="e.g., Einstein, STEM-A, ABM-1"
                  />
                  {field.state.meta.errors.length > 0 && (
                    <p className="m-0 text-xs text-destructive">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Grade Level */}
            <form.Field
              name="gradeLevel"
              validators={{
                onChange: sectionSchema.shape.gradeLevel,
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>
                    Grade Level <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={field.state.value.toString()}
                    onValueChange={(value) => field.handleChange(Number(value))}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select grade level" />
                    </SelectTrigger>
                    <SelectContent>
                      {[7, 8, 9, 10, 11, 12].map((grade) => (
                        <SelectItem key={grade} value={grade.toString()}>
                          Grade {grade}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.length > 0 && (
                    <p className="m-0 text-xs text-destructive">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Academic Year */}
            <form.Field
              name="academicYearId"
              validators={{
                onChange: sectionSchema.shape.academicYearId,
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>
                    Academic Year <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={field.state.value || ''}
                    onValueChange={(value) => field.handleChange(value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select academic year" />
                    </SelectTrigger>
                    <SelectContent>
                      {academicYears?.map((ay) => (
                        <SelectItem key={ay._id} value={ay._id}>
                          {ay.schoolYear}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.length > 0 && (
                    <p className="m-0 text-xs text-destructive">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {/* Adviser */}
            <form.Field
              name="adviserId"
              validators={{
                onChange: sectionSchema.shape.adviserId,
              }}
            >
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>
                    Adviser <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={field.state.value || ''}
                    onValueChange={(value) => field.handleChange(value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an adviser" />
                    </SelectTrigger>
                    <SelectContent>
                      {teachers?.map((teacher) => (
                        <SelectItem key={teacher._id} value={teacher._id}>
                          {[teacher.firstName, teacher.middleName, teacher.lastName]
                            .filter(Boolean)
                            .join(' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.length > 0 && (
                    <p className="m-0 text-xs text-destructive">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  )}
                </div>
              )}
            </form.Field>
          </div>

          <form.Subscribe
            selector={(state) => state.values.gradeLevel}
            children={(gradeLevel) =>
              isSeniorHighSchool(gradeLevel) && (
                <>
                  <h3 className="text-md font-semibold">Senior High School Details</h3>
                  <div className="space-y-4">
                    {/* Track */}
                    <form.Field name="trackId">
                      {(field) => (
                        <div className="flex items-center gap-4">
                          <Label htmlFor={field.name} className="w-20 text-right">
                            Track
                          </Label>
                          <div className="flex-1">
                            <Select
                              value={field.state.value || 'none'}
                              onValueChange={(value) => {
                                const trackValue = value === 'none' ? undefined : (value as TrackId)
                                field.handleChange(trackValue)
                              }}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a track (optional)" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">No track</SelectItem>
                                {tracks?.map((track) => (
                                  <SelectItem key={track._id} value={track._id}>
                                    {track.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}
                    </form.Field>

                    {/* Strand */}
                    <form.Subscribe
                      selector={(state) => state.values.trackId}
                      children={(currentTrackId) => (
                        <form.Field name="strandId">
                          {(field) => {
                            const availableStrands =
                              currentTrackId && strands
                                ? strands.filter((strand) => strand.trackId === currentTrackId)
                                : []
                            return (
                              <div className="flex items-center gap-4">
                                <Label htmlFor={field.name} className="w-20 text-right">
                                  Strand
                                </Label>
                                <div className="flex-1">
                                  <Select
                                    value={field.state.value || 'none'}
                                    onValueChange={(value) =>
                                      field.handleChange(
                                        value === 'none' ? undefined : (value as StrandId)
                                      )
                                    }
                                    disabled={!currentTrackId}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="Select a strand (optional)" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No strand</SelectItem>
                                      {availableStrands.map((strand) => (
                                        <SelectItem key={strand._id} value={strand._id}>
                                          {strand.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            )
                          }}
                        </form.Field>
                      )}
                    />

                    {/* Major */}
                    <form.Subscribe
                      selector={(state) => state.values.strandId}
                      children={(currentStrandId) => (
                        <form.Field name="majorId">
                          {(field) => {
                            const availableMajors =
                              currentStrandId && majors
                                ? majors.filter((major) => major.strandId === currentStrandId)
                                : []
                            return (
                              <div className="flex items-center gap-4">
                                <Label htmlFor={field.name} className="w-20 text-right">
                                  Major
                                </Label>
                                <div className="flex-1">
                                  <Select
                                    value={field.state.value || 'none'}
                                    onValueChange={(value) =>
                                      field.handleChange(
                                        value === 'none' ? undefined : (value as MajorId)
                                      )
                                    }
                                    disabled={!currentStrandId}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="Select a major (optional)" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">No major</SelectItem>
                                      {availableMajors.map((major) => (
                                        <SelectItem key={major._id} value={major._id}>
                                          {major.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            )
                          }}
                        </form.Field>
                      )}
                    />
                  </div>
                </>
              )
            }
          />

          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button type="submit" disabled={!canSubmit || isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Spinner variant="circle" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <CirclePlusIcon />
                      Create
                    </>
                  )}
                </Button>
              )}
            />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
