import { query, mutation } from './_generated/server'
import { ConvexError, v } from 'convex/values'

// Queries
export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('sections').collect()
  },
})

export const getActive = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('sections')
      .filter((q) => q.eq(q.field('isActive'), true))
      .collect()
  },
})

export const getById = query({
  args: { id: v.id('sections') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const getByGradeLevel = query({
  args: { gradeLevel: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('sections')
      .withIndex('by_grade_level', (q) => q.eq('gradeLevel', args.gradeLevel))
      .collect()
  },
})

export const getByAcademicYear = query({
  args: { academicYearId: v.id('academicYears') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('sections')
      .withIndex('by_academic_year', (q) => q.eq('academicYearId', args.academicYearId))
      .collect()
  },
})

export const getByAdviser = query({
  args: { adviserId: v.id('teachers') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('sections')
      .withIndex('by_adviser', (q) => q.eq('adviserId', args.adviserId))
      .collect()
  },
})

// Mutations
export const create = mutation({
  args: {
    name: v.string(),
    gradeLevel: v.number(),
    academicYearId: v.id('academicYears'),
    adviserId: v.id('teachers'),
    trackId: v.optional(v.id('tracks')),
    strandId: v.optional(v.id('strands')),
    majorId: v.optional(v.id('majors')),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate that academic year exists
    const academicYear = await ctx.db.get(args.academicYearId)
    if (!academicYear) {
      throw new ConvexError()
      throw new Error('Academic year not found')
    }

    // Validate that adviser exists and is active
    const adviser = await ctx.db.get(args.adviserId)
    if (!adviser) {
      throw new Error('Adviser not found')
    }
    if (!adviser.isActive) {
      throw new Error('Adviser is not active')
    }

    // Validate track/strand/major relationships if provided
    if (args.trackId) {
      const track = await ctx.db.get(args.trackId)
      if (!track) {
        throw new Error('Track not found')
      }
    }

    if (args.strandId) {
      const strand = await ctx.db.get(args.strandId)
      if (!strand) {
        throw new Error('Strand not found')
      }
      // Validate strand belongs to track if track is provided
      if (args.trackId && strand.trackId !== args.trackId) {
        throw new Error('Strand does not belong to the specified track')
      }
    }

    if (args.majorId) {
      const major = await ctx.db.get(args.majorId)
      if (!major) {
        throw new Error('Major not found')
      }
      // Validate major belongs to strand if strand is provided
      if (args.strandId && major.strandId !== args.strandId) {
        throw new Error('Major does not belong to the specified strand')
      }
    }

    // Check for duplicate section name within the same academic year and grade level
    const existingSection = await ctx.db
      .query('sections')
      .filter((q) =>
        q.and(
          q.eq(q.field('name'), args.name),
          q.eq(q.field('gradeLevel'), args.gradeLevel),
          q.eq(q.field('academicYearId'), args.academicYearId),
          q.eq(q.field('isActive'), true)
        )
      )
      .first()

    if (existingSection) {
      throw new Error(
        'A section with this name already exists for this grade level and academic year'
      )
    }

    // Create the section
    return await ctx.db.insert('sections', {
      name: args.name,
      gradeLevel: args.gradeLevel,
      academicYearId: args.academicYearId,
      adviserId: args.adviserId,
      trackId: args.trackId,
      strandId: args.strandId,
      majorId: args.majorId,
      maleCount: 0, // Initialize with 0 students
      femaleCount: 0, // Initialize with 0 students
      isActive: args.isActive ?? true,
    })
  },
})

export const update = mutation({
  args: {
    id: v.id('sections'),
    name: v.optional(v.string()),
    gradeLevel: v.optional(v.number()),
    academicYearId: v.optional(v.id('academicYears')),
    adviserId: v.optional(v.id('teachers')),
    trackId: v.optional(v.id('tracks')),
    strandId: v.optional(v.id('strands')),
    majorId: v.optional(v.id('majors')),
    maleCount: v.optional(v.number()),
    femaleCount: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args

    // Check if section exists
    const existingSection = await ctx.db.get(id)
    if (!existingSection) {
      throw new Error('Section not found')
    }

    // Validate academic year if provided
    if (updates.academicYearId) {
      const academicYear = await ctx.db.get(updates.academicYearId)
      if (!academicYear) {
        throw new Error('Academic year not found')
      }
    }

    // Validate adviser if provided
    if (updates.adviserId) {
      const adviser = await ctx.db.get(updates.adviserId)
      if (!adviser) {
        throw new Error('Adviser not found')
      }
      if (!adviser.isActive) {
        throw new Error('Adviser is not active')
      }
    }

    // Validate track/strand/major relationships if provided
    if (updates.trackId) {
      const track = await ctx.db.get(updates.trackId)
      if (!track) {
        throw new Error('Track not found')
      }
    }

    if (updates.strandId) {
      const strand = await ctx.db.get(updates.strandId)
      if (!strand) {
        throw new Error('Strand not found')
      }
      // Use updated trackId or existing one
      const trackId = updates.trackId ?? existingSection.trackId
      if (trackId && strand.trackId !== trackId) {
        throw new Error('Strand does not belong to the specified track')
      }
    }

    if (updates.majorId) {
      const major = await ctx.db.get(updates.majorId)
      if (!major) {
        throw new Error('Major not found')
      }
      // Use updated strandId or existing one
      const strandId = updates.strandId ?? existingSection.strandId
      if (strandId && major.strandId !== strandId) {
        throw new Error('Major does not belong to the specified strand')
      }
    }

    // Check for duplicate section name if name is being updated
    if (updates.name && updates.name !== existingSection.name) {
      const academicYearId = updates.academicYearId ?? existingSection.academicYearId
      const gradeLevel = updates.gradeLevel ?? existingSection.gradeLevel

      const duplicateSection = await ctx.db
        .query('sections')
        .filter((q) =>
          q.and(
            q.eq(q.field('name'), updates.name),
            q.eq(q.field('gradeLevel'), gradeLevel),
            q.eq(q.field('academicYearId'), academicYearId),
            q.eq(q.field('isActive'), true),
            q.neq(q.field('_id'), id)
          )
        )
        .first()

      if (duplicateSection) {
        throw new Error(
          'A section with this name already exists for this grade level and academic year'
        )
      }
    }

    // Update the section
    await ctx.db.patch(id, updates)
    return id
  },
})

export const remove = mutation({
  args: { id: v.id('sections') },
  handler: async (ctx, args) => {
    // Check if section exists
    const section = await ctx.db.get(args.id)
    if (!section) {
      throw new Error('Section not found')
    }

    // Hard delete - permanently remove from database
    await ctx.db.delete(args.id)
    return args.id
  },
})

export const updateStudentCounts = mutation({
  args: {
    id: v.id('sections'),
    maleCount: v.number(),
    femaleCount: v.number(),
  },
  handler: async (ctx, args) => {
    // Check if section exists
    const section = await ctx.db.get(args.id)
    if (!section) {
      throw new Error('Section not found')
    }

    // Validate counts are non-negative
    if (args.maleCount < 0 || args.femaleCount < 0) {
      throw new Error('Student counts must be non-negative')
    }

    // Update student counts
    await ctx.db.patch(args.id, {
      maleCount: args.maleCount,
      femaleCount: args.femaleCount,
    })
    return args.id
  },
})
